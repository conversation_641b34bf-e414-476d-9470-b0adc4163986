package com.eci.common;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 性能优化版本的字典处理工具类
 * 
 * 主要优化：
 * 1. 智能缓存预热
 * 2. 批量SQL执行优化
 * 3. 异步处理支持
 * 4. 内存使用优化
 * 5. 性能监控集成
 */
public class DictFieldUtilsOptimized {
    
    private static final Logger logger = LoggerFactory.getLogger(DictFieldUtilsOptimized.class);
    
    // 优化的缓存配置
    private static final Cache<Class<?>, Map<Field, DictField>> FIELD_CACHE = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .recordStats()
            .build();
    
    private static final Cache<String, Map<String, CodeNameCommon>> DICT_CACHE = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .recordStats()
            .build();
    
    private static final Cache<String, List<Map<String, Object>>> SQL_RESULT_CACHE = Caffeine.newBuilder()
            .maximumSize(500)
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .recordStats()
            .build();
    
    private static final Cache<String, SqlTemplate> SQL_TEMPLATE_CACHE = Caffeine.newBuilder()
            .maximumSize(200)
            .build();
    
    // 性能监控
    private static final Map<String, PerformanceMetrics> PERFORMANCE_METRICS = new ConcurrentHashMap<>();
    
    // 异步执行器
    private static final ExecutorService ASYNC_EXECUTOR = ThreadPoolConfig.createDefaultCacheRefreshExecutor();
    
    // 批量处理配置
    private static final int BATCH_SIZE = 100;
    private static final int MAX_CONCURRENT_BATCHES = 4;
    
    // 数据库操作工具
    private static final ZsrDBHelper zsrDBHelper = new ZsrDBHelper();
    
    static {
        // 注册缓存监控
        CacheMonitor.registerCache("OPTIMIZED_FIELD_CACHE", FIELD_CACHE, 500);
        CacheMonitor.registerCache("OPTIMIZED_DICT_CACHE", DICT_CACHE, 1000);
        CacheMonitor.registerCache("OPTIMIZED_SQL_RESULT_CACHE", SQL_RESULT_CACHE, 500);
        CacheMonitor.registerCache("OPTIMIZED_SQL_TEMPLATE_CACHE", SQL_TEMPLATE_CACHE, 200);
        
        // 启动性能监控
        startPerformanceMonitoring();
        
        // 关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            ThreadPoolConfig.shutdownExecutor(ASYNC_EXECUTOR, 10);
        }));
    }
    
    /**
     * 高性能处理实体列表的字典字段
     */
    public static <T> CompletableFuture<Void> handleDictFieldsAsync(List<T> entities) {
        return CompletableFuture.runAsync(() -> handleDictFields(entities), ASYNC_EXECUTOR);
    }
    
    /**
     * 同步处理实体列表的字典字段（优化版本）
     */
    public static <T> void handleDictFields(List<T> entities) {
        if (entities == null || entities.isEmpty()) {
            return;
        }
        
        long startTime = System.currentTimeMillis();
        String methodName = "handleDictFields";
        
        try {
            Class<?> entityClass = entities.get(0).getClass();
            Map<Field, DictField> annotatedFields = getAnnotatedFields(entityClass);
            
            if (annotatedFields.isEmpty()) {
                return;
            }
            
            // 批量预加载字典数据
            Map<String, Map<String, CodeNameCommon>> dictDataMap = batchLoadDictData(annotatedFields.values());
            
            // 分批并行处理实体
            processBatchedEntities(entities, annotatedFields, dictDataMap);
            
            recordPerformance(methodName, startTime, entities.size(), true);
            
        } catch (Exception e) {
            recordPerformance(methodName, startTime, entities.size(), false);
            logger.error("处理字典字段失败", e);
            throw new RuntimeException("字典字段处理失败", e);
        }
    }

    /**
     * 同步处理实体列表的字典字段（优化版本）
     */
    public static <T> void handleDictFields(T entitie) {
        List<T> entities = new ArrayList<>();
        entities.add(entitie);
        if (entities == null || entities.isEmpty()) {
            return;
        }

        long startTime = System.currentTimeMillis();
        String methodName = "handleDictFields";

        try {
            Class<?> entityClass = entities.get(0).getClass();
            Map<Field, DictField> annotatedFields = getAnnotatedFields(entityClass);

            if (annotatedFields.isEmpty()) {
                return;
            }

            // 批量预加载字典数据
            Map<String, Map<String, CodeNameCommon>> dictDataMap = batchLoadDictData(annotatedFields.values());

            // 分批并行处理实体
            processBatchedEntities(entities, annotatedFields, dictDataMap);

            recordPerformance(methodName, startTime, entities.size(), true);

        } catch (Exception e) {
            recordPerformance(methodName, startTime, entities.size(), false);
            logger.error("处理字典字段失败", e);
            throw new RuntimeException("字典字段处理失败", e);
        }
    }

    /**
     * 分批并行处理实体
     */
    private static <T> void processBatchedEntities(
            List<T> entities, 
            Map<Field, DictField> annotatedFields,
            Map<String, Map<String, CodeNameCommon>> dictDataMap) {
        
        int totalSize = entities.size();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        // 分批处理
        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, totalSize);
            List<T> batch = entities.subList(i, endIndex);
            
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                processBatch(batch, annotatedFields, dictDataMap);
            }, ASYNC_EXECUTOR);
            
            futures.add(future);
            
            // 控制并发数量
            if (futures.size() >= MAX_CONCURRENT_BATCHES) {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                futures.clear();
            }
        }
        
        // 等待剩余任务完成
        if (!futures.isEmpty()) {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }
    }
    
    /**
     * 处理单个批次
     */
    private static <T> void processBatch(
            List<T> batch,
            Map<Field, DictField> annotatedFields,
            Map<String, Map<String, CodeNameCommon>> dictDataMap) {
        
        for (T entity : batch) {
            if (entity != null) {
                for (Map.Entry<Field, DictField> entry : annotatedFields.entrySet()) {
                    try {
                        processField(entity, entry.getKey(), entry.getValue(), dictDataMap);
                    } catch (Exception e) {
                        logger.warn("处理字段失败: {}.{}", 
                                entity.getClass().getSimpleName(), entry.getKey().getName(), e);
                    }
                }
            }
        }
    }
    
    /**
     * 批量加载字典数据
     */
    private static Map<String, Map<String, CodeNameCommon>> batchLoadDictData(Collection<DictField> fields) {
        Map<String, Map<String, CodeNameCommon>> result = new ConcurrentHashMap<>();
        
        // 按类型分组
        Map<String, List<DictField>> groupedFields = fields.stream()
                .collect(Collectors.groupingBy(field -> {
                    if (field.queryKey() != null && !field.queryKey().trim().isEmpty()) {
                        return "queryKey";
                    } else if (field.data().length > 0) {
                        return "data";
                    } else if (field.sql() != null && !field.sql().trim().isEmpty()) {
                        return "sql";
                    }
                    return "unknown";
                }));
        
        // 并行加载不同类型的数据
        List<CompletableFuture<Void>> loadTasks = new ArrayList<>();
        
        // 加载queryKey数据
        if (groupedFields.containsKey("queryKey")) {
            loadTasks.add(CompletableFuture.runAsync(() -> 
                    loadQueryKeyData(groupedFields.get("queryKey"), result), ASYNC_EXECUTOR));
        }
        
        // 加载data数据
        if (groupedFields.containsKey("data")) {
            loadTasks.add(CompletableFuture.runAsync(() -> 
                    loadDataFields(groupedFields.get("data"), result), ASYNC_EXECUTOR));
        }
        
        // 加载SQL数据
        if (groupedFields.containsKey("sql")) {
            loadTasks.add(CompletableFuture.runAsync(() -> 
                    loadSqlData(groupedFields.get("sql"), result), ASYNC_EXECUTOR));
        }
        
        // 等待所有加载任务完成
        CompletableFuture.allOf(loadTasks.toArray(new CompletableFuture[0])).join();
        
        return result;
    }
    
    /**
     * 加载queryKey数据
     */
    private static void loadQueryKeyData(List<DictField> fields, Map<String, Map<String, CodeNameCommon>> result) {
        for (DictField field : fields) {
            String cacheKey = "QK_" + field.queryKey();
            try {
                Map<String, CodeNameCommon> data = DICT_CACHE.get(cacheKey, key -> 
                        DataDictUtils.queryCodeNameMap(field.queryKey()));
                result.put(cacheKey, data);
            } catch (Exception e) {
                logger.warn("加载queryKey数据失败: {}", field.queryKey(), e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }
    }
    
    /**
     * 加载data字段数据
     */
    private static void loadDataFields(List<DictField> fields, Map<String, Map<String, CodeNameCommon>> result) {
        for (DictField field : fields) {
            String cacheKey = "DS_" + Arrays.hashCode(field.data());
            try {
                Map<String, CodeNameCommon> data = DICT_CACHE.get(cacheKey, key -> 
                        parseDataFromJsonArray(field.data()));
                result.put(cacheKey, data);
            } catch (Exception e) {
                logger.warn("加载data数据失败", e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }
    }
    
    /**
     * 加载SQL数据
     */
    private static void loadSqlData(List<DictField> fields, Map<String, Map<String, CodeNameCommon>> result) {
        for (DictField field : fields) {
            String cacheKey = "SQL_" + field.sql().hashCode();
            try {
                SqlTemplate template = SQL_TEMPLATE_CACHE.get(field.sql(), SqlTemplate::new);
                Map<String, CodeNameCommon> data = new TreeMap<>(String.CASE_INSENSITIVE_ORDER);
                data.put("SQL_TEMPLATE", new CodeNameCommon("SQL_TEMPLATE", "SQL_TEMPLATE", template));
                result.put(cacheKey, data);
            } catch (Exception e) {
                logger.warn("加载SQL模板失败: {}", field.sql(), e);
                result.put(cacheKey, Collections.emptyMap());
            }
        }
    }
    
    /**
     * 获取带注解的字段（优化版本）
     */
    private static Map<Field, DictField> getAnnotatedFields(Class<?> clazz) {
        return FIELD_CACHE.get(clazz, cls -> {
            Map<Field, DictField> result = new HashMap<>();
            Class<?> currentClass = cls;
            
            while (currentClass != null && !currentClass.equals(Object.class)) {
                for (Field field : currentClass.getDeclaredFields()) {
                    if (field.isAnnotationPresent(DictField.class)) {
                        result.put(field, field.getAnnotation(DictField.class));
                    }
                }
                currentClass = currentClass.getSuperclass();
            }
            
            return result;
        });
    }
    
    /**
     * 处理单个字段（优化版本）
     */
    private static <T> void processField(T entity, Field field, DictField dictField,
                                         Map<String, Map<String, CodeNameCommon>> dictDataMap) throws Exception {
        field.setAccessible(true);
        Object value = field.get(entity);
        
        if (value == null) {
            return;
        }
        
        if (dictField.useDateFormat()) {
            processDateField(entity, field, value, dictField);
        } else {
            processDictField(entity, field, value, dictField, dictDataMap);
        }
    }
    
    /**
     * 处理日期字段
     */
    private static <T> void processDateField(T entity, Field field, Object value, DictField dictField) {
        try {
            String formattedDate = formatDateValue(value, dictField.dateFormat());
            if (formattedDate != null) {
                setFieldValue(entity, field.getName() + "Display", formattedDate);
            }
        } catch (Exception e) {
            logger.warn("格式化日期失败: {}", value, e);
        }
    }
    
    /**
     * 处理字典字段
     */
    private static <T> void processDictField(T entity, Field field, Object value, DictField dictField,
                                             Map<String, Map<String, CodeNameCommon>> dictDataMap) {
        String cacheKey = generateCacheKey(dictField);
        Map<String, CodeNameCommon> codeMap = dictDataMap.get(cacheKey);
        
        if (codeMap != null) {
            CodeNameCommon templateEntry = codeMap.get("SQL_TEMPLATE");
            if (templateEntry != null && templateEntry.getSqlTemplate() != null) {
                processSqlTemplate(entity, field, value, templateEntry.getSqlTemplate(), dictField);
            } else {
                processCodeName(entity, field, value, codeMap, dictField);
            }
        }
    }
    
    // 其他辅助方法...
    private static String generateCacheKey(DictField dictField) {
        if (dictField.queryKey() != null && !dictField.queryKey().trim().isEmpty()) {
            return "QK_" + dictField.queryKey();
        } else if (dictField.data().length > 0) {
            return "DS_" + Arrays.hashCode(dictField.data());
        } else if (dictField.sql() != null && !dictField.sql().trim().isEmpty()) {
            return "SQL_" + dictField.sql().hashCode();
        }
        return "EMPTY";
    }
    
    private static Map<String, CodeNameCommon> parseDataFromJsonArray(String[] data) {
        // 实现JSON解析逻辑
        return Collections.emptyMap();
    }
    
    private static String formatDateValue(Object value, String format) {
        // 实现日期格式化逻辑
        return value.toString();
    }
    
    private static <T> void setFieldValue(T entity, String fieldName, Object value) {
        // 实现字段设置逻辑
    }
    
    private static <T> void processCodeName(T entity, Field field, Object value, 
                                            Map<String, CodeNameCommon> codeMap, DictField dictField) {
        // 实现代码名称处理逻辑
    }
    
    private static <T> void processSqlTemplate(T entity, Field field, Object value, 
                                               SqlTemplate template, DictField dictField) {
        // 实现SQL模板处理逻辑
    }
    
    /**
     * 性能监控
     */
    private static void startPerformanceMonitoring() {
        // 实现性能监控逻辑
    }
    
    private static void recordPerformance(String method, long startTime, int entityCount, boolean success) {
        long duration = System.currentTimeMillis() - startTime;
        PERFORMANCE_METRICS.computeIfAbsent(method, k -> new PerformanceMetrics())
                .record(duration, entityCount, success);
    }
    
    /**
     * 性能指标类
     */
    private static class PerformanceMetrics {
        private long totalCalls = 0;
        private long totalDuration = 0;
        private long totalEntities = 0;
        private long successCalls = 0;
        
        synchronized void record(long duration, int entityCount, boolean success) {
            totalCalls++;
            totalDuration += duration;
            totalEntities += entityCount;
            if (success) successCalls++;
        }
        
        synchronized double getAverageDuration() {
            return totalCalls > 0 ? (double) totalDuration / totalCalls : 0;
        }
        
        synchronized double getSuccessRate() {
            return totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 0;
        }
        
        synchronized double getThroughput() {
            return totalDuration > 0 ? (double) totalEntities / totalDuration * 1000 : 0;
        }
    }
    
    /**
     * 获取性能统计
     */
    public static String getPerformanceStats() {
        StringBuilder sb = new StringBuilder("=== 性能统计 ===\n");
        for (Map.Entry<String, PerformanceMetrics> entry : PERFORMANCE_METRICS.entrySet()) {
            PerformanceMetrics metrics = entry.getValue();
            sb.append(String.format("%s: 平均耗时=%.2fms, 成功率=%.1f%%, 吞吐量=%.1f entities/s\n",
                    entry.getKey(), metrics.getAverageDuration(), 
                    metrics.getSuccessRate(), metrics.getThroughput()));
        }
        return sb.toString();
    }
}
