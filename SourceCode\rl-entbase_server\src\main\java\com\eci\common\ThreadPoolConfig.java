package com.eci.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 线程池配置工具类
 * 提供安全的线程池创建和管理功能
 */
public class ThreadPoolConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(ThreadPoolConfig.class);
    
    /**
     * 创建缓存刷新专用的线程池
     * 
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param keepAliveTime 线程空闲时间
     * @param timeUnit 时间单位
     * @param queueCapacity 队列容量
     * @param threadNamePrefix 线程名前缀
     * @return 配置好的线程池
     */
    public static ScheduledExecutorService createCacheRefreshExecutor(
            int corePoolSize, 
            int maxPoolSize,
            long keepAliveTime,
            TimeUnit timeUnit,
            int queueCapacity,
            String threadNamePrefix) {
        
        AtomicInteger threadNumber = new AtomicInteger(1);
        
        ThreadFactory threadFactory = r -> {
            Thread t = new Thread(r, threadNamePrefix + "-" + threadNumber.getAndIncrement());
            t.setDaemon(true);
            t.setPriority(Thread.NORM_PRIORITY);
            return t;
        };
        
        // 使用ScheduledThreadPoolExecutor，它继承自ThreadPoolExecutor
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
                corePoolSize, 
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
        );
        
        // 设置核心线程超时
        executor.setKeepAliveTime(keepAliveTime, timeUnit);
        executor.allowCoreThreadTimeOut(true);
        
        // 设置移除取消任务
        executor.setRemoveOnCancelPolicy(true);
        
        logger.info("创建缓存刷新线程池: core={}, max={}, keepAlive={}{}",
                corePoolSize, maxPoolSize, keepAliveTime, timeUnit);
        
        return executor;
    }
    
    /**
     * 创建默认的缓存刷新线程池
     */
    public static ScheduledExecutorService createDefaultCacheRefreshExecutor() {
        return createCacheRefreshExecutor(
                2,              // 核心线程数
                4,              // 最大线程数  
                60,             // 空闲时间
                TimeUnit.SECONDS, // 时间单位
                100,            // 队列容量
                "CacheRefresh"  // 线程名前缀
        );
    }
    
    /**
     * 安全关闭线程池
     * 
     * @param executor 要关闭的线程池
     * @param timeoutSeconds 等待超时时间（秒）
     */
    public static void shutdownExecutor(ExecutorService executor, int timeoutSeconds) {
        if (executor == null || executor.isShutdown()) {
            return;
        }
        
        try {
            logger.info("开始关闭线程池...");
            
            // 停止接收新任务
            executor.shutdown();
            
            // 等待现有任务完成
            if (!executor.awaitTermination(timeoutSeconds, TimeUnit.SECONDS)) {
                logger.warn("线程池在 {} 秒内未能正常关闭，强制关闭", timeoutSeconds);
                
                // 强制关闭
                executor.shutdownNow();
                
                // 再次等待
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    logger.error("线程池强制关闭失败");
                }
            }
            
            logger.info("线程池已成功关闭");
            
        } catch (InterruptedException e) {
            logger.warn("关闭线程池时被中断，强制关闭");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 获取线程池状态信息
     */
    public static String getExecutorStats(ThreadPoolExecutor executor) {
        if (executor == null) {
            return "线程池为空";
        }
        
        return String.format(
                "线程池状态: 活跃线程=%d, 核心线程=%d, 最大线程=%d, 池大小=%d, 队列大小=%d, 已完成任务=%d",
                executor.getActiveCount(),
                executor.getCorePoolSize(),
                executor.getMaximumPoolSize(),
                executor.getPoolSize(),
                executor.getQueue().size(),
                executor.getCompletedTaskCount()
        );
    }
    
    /**
     * 检查线程池健康状态
     */
    public static boolean isExecutorHealthy(ThreadPoolExecutor executor) {
        if (executor == null || executor.isShutdown() || executor.isTerminated()) {
            return false;
        }
        
        // 检查队列是否过满
        int queueSize = executor.getQueue().size();
        int maxQueueSize = 1000; // 假设最大队列大小
        
        if (queueSize > maxQueueSize * 0.8) {
            logger.warn("线程池队列使用率过高: {}/{}", queueSize, maxQueueSize);
            return false;
        }
        
        // 检查活跃线程数是否异常
        int activeCount = executor.getActiveCount();
        int maxPoolSize = executor.getMaximumPoolSize();
        
        if (activeCount > maxPoolSize * 0.9) {
            logger.warn("线程池活跃线程数过高: {}/{}", activeCount, maxPoolSize);
            return false;
        }
        
        return true;
    }
    
    /**
     * 线程池监控器
     */
    public static class ExecutorMonitor {
        private final ThreadPoolExecutor executor;
        private final String name;
        private final ScheduledExecutorService monitorExecutor;
        
        public ExecutorMonitor(ThreadPoolExecutor executor, String name) {
            this.executor = executor;
            this.name = name;
            this.monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
                Thread t = new Thread(r, "ExecutorMonitor-" + name);
                t.setDaemon(true);
                return t;
            });
        }
        
        /**
         * 开始监控
         */
        public void startMonitoring(long period, TimeUnit timeUnit) {
            monitorExecutor.scheduleAtFixedRate(() -> {
                try {
                    String stats = getExecutorStats(executor);
                    boolean healthy = isExecutorHealthy(executor);
                    
                    if (!healthy) {
                        logger.warn("线程池 {} 状态异常: {}", name, stats);
                    } else {
                        logger.debug("线程池 {} 状态正常: {}", name, stats);
                    }
                } catch (Exception e) {
                    logger.error("监控线程池 {} 时发生异常", name, e);
                }
            }, period, period, timeUnit);
        }
        
        /**
         * 停止监控
         */
        public void stopMonitoring() {
            shutdownExecutor(monitorExecutor, 5);
        }
    }
    
    /**
     * 自定义拒绝策略：记录日志并使用调用者运行
     */
    public static class LoggingCallerRunsPolicy implements RejectedExecutionHandler {
        private static final Logger logger = LoggerFactory.getLogger(LoggingCallerRunsPolicy.class);
        
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            logger.warn("线程池队列已满，任务将在调用者线程中执行: {}", r.getClass().getSimpleName());
            
            if (!executor.isShutdown()) {
                r.run();
            }
        }
    }
    
    /**
     * 自定义拒绝策略：丢弃任务并记录日志
     */
    public static class LoggingDiscardPolicy implements RejectedExecutionHandler {
        private static final Logger logger = LoggerFactory.getLogger(LoggingDiscardPolicy.class);
        
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            logger.warn("线程池队列已满，丢弃任务: {}", r.getClass().getSimpleName());
        }
    }
}
