package com.eci.common;

import com.github.benmanes.caffeine.cache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 缓存监控工具类
 * 提供缓存使用情况监控、统计和报警功能
 */
public class CacheMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(CacheMonitor.class);
    
    // 缓存实例注册表
    private static final Map<String, CacheInfo> CACHE_REGISTRY = new ConcurrentHashMap<>();
    
    // 监控定时器
    private static final ScheduledExecutorService MONITOR_EXECUTOR = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread t = new Thread(r, "CacheMonitor");
        t.setDaemon(true);
        return t;
    });
    
    // 监控配置
    private static MonitorConfig config = new MonitorConfig();
    
    // 静态初始化
    static {
        // 启动定时监控
        startPeriodicMonitoring();
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            MONITOR_EXECUTOR.shutdown();
            try {
                if (!MONITOR_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                    MONITOR_EXECUTOR.shutdownNow();
                }
            } catch (InterruptedException e) {
                MONITOR_EXECUTOR.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }));
    }
    
    /**
     * 注册Caffeine缓存进行监控
     */
    public static void registerCache(String name, Cache<?, ?> cache, long maxSize) {
        CACHE_REGISTRY.put(name, new CaffeineInfo(name, cache, maxSize));
        logger.info("已注册缓存监控: {}, 最大大小: {}", name, maxSize);
    }
    
    /**
     * 注册Map缓存进行监控
     */
    public static void registerCache(String name, Map<?, ?> cache, long maxSize) {
        CACHE_REGISTRY.put(name, new MapCacheInfo(name, cache, maxSize));
        logger.info("已注册Map缓存监控: {}, 最大大小: {}", name, maxSize);
    }
    
    /**
     * 注销缓存监控
     */
    public static void unregisterCache(String name) {
        CACHE_REGISTRY.remove(name);
        logger.info("已注销缓存监控: {}", name);
    }
    
    /**
     * 获取所有缓存统计信息
     */
    public static String getAllCacheStats() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 缓存监控报告 ===\n");
        
        for (CacheInfo info : CACHE_REGISTRY.values()) {
            sb.append(info.getStats()).append("\n");
        }
        
        return sb.toString();
    }
    
    /**
     * 获取指定缓存统计信息
     */
    public static String getCacheStats(String name) {
        CacheInfo info = CACHE_REGISTRY.get(name);
        return info != null ? info.getStats() : "缓存不存在: " + name;
    }
    
    /**
     * 检查缓存健康状态
     */
    public static boolean checkCacheHealth() {
        boolean allHealthy = true;
        
        for (CacheInfo info : CACHE_REGISTRY.values()) {
            if (!info.isHealthy()) {
                logger.warn("缓存健康检查失败: {}", info.getStats());
                allHealthy = false;
            }
        }
        
        return allHealthy;
    }
    
    /**
     * 启动定期监控
     */
    private static void startPeriodicMonitoring() {
        MONITOR_EXECUTOR.scheduleAtFixedRate(() -> {
            try {
                if (config.isEnablePeriodicLogging()) {
                    logger.info(getAllCacheStats());
                }
                
                if (config.isEnableHealthCheck()) {
                    checkCacheHealth();
                }
                
                if (config.isEnableMemoryAlert()) {
                    checkMemoryUsage();
                }
            } catch (Exception e) {
                logger.error("缓存监控任务执行失败", e);
            }
        }, config.getMonitorInterval(), config.getMonitorInterval(), TimeUnit.MINUTES);
    }
    
    /**
     * 检查内存使用情况
     */
    private static void checkMemoryUsage() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        double usagePercent = (double) usedMemory / totalMemory * 100;
        
        if (usagePercent > config.getMemoryAlertThreshold()) {
            logger.warn("内存使用率过高: {:.2f}%, 已用: {} MB, 总计: {} MB", 
                    usagePercent, usedMemory / 1024 / 1024, totalMemory / 1024 / 1024);
            
            // 触发缓存清理
            if (config.isEnableAutoCleanup()) {
                performCacheCleanup();
            }
        }
    }
    
    /**
     * 执行缓存清理
     */
    private static void performCacheCleanup() {
        logger.info("开始执行缓存清理...");
        
        for (CacheInfo info : CACHE_REGISTRY.values()) {
            if (info.getUsagePercent() > config.getCacheCleanupThreshold()) {
                info.cleanup();
                logger.info("已清理缓存: {}", info.getName());
            }
        }
    }
    
    /**
     * 缓存信息抽象类
     */
    public abstract static class CacheInfo {
        protected final String name;
        protected final long maxSize;
        
        public CacheInfo(String name, long maxSize) {
            this.name = name;
            this.maxSize = maxSize;
        }
        
        public String getName() {
            return name;
        }
        
        public long getMaxSize() {
            return maxSize;
        }
        
        public abstract long getCurrentSize();
        public abstract double getUsagePercent();
        public abstract String getStats();
        public abstract boolean isHealthy();
        public abstract void cleanup();
    }
    
    /**
     * Caffeine缓存信息
     */
    public static class CaffeineInfo extends CacheInfo {
        private final Cache<?, ?> cache;
        
        public CaffeineInfo(String name, Cache<?, ?> cache, long maxSize) {
            super(name, maxSize);
            this.cache = cache;
        }
        
        @Override
        public long getCurrentSize() {
            return cache.estimatedSize();
        }
        
        @Override
        public double getUsagePercent() {
            return maxSize > 0 ? (double) getCurrentSize() / maxSize * 100 : 0;
        }
        
        @Override
        public String getStats() {
            com.github.benmanes.caffeine.cache.stats.CacheStats stats = cache.stats();
            return String.format(
                    "缓存[%s]: 大小=%d/%d (%.1f%%), 命中率=%.2f%%, 驱逐=%d",
                    name, getCurrentSize(), maxSize, getUsagePercent(),
                    stats.hitRate() * 100, stats.evictionCount()
            );
        }
        
        @Override
        public boolean isHealthy() {
            double usage = getUsagePercent();
            return usage < config.getCacheHealthThreshold();
        }
        
        @Override
        public void cleanup() {
            cache.cleanUp();
        }
    }
    
    /**
     * Map缓存信息
     */
    public static class MapCacheInfo extends CacheInfo {
        private final Map<?, ?> cache;
        
        public MapCacheInfo(String name, Map<?, ?> cache, long maxSize) {
            super(name, maxSize);
            this.cache = cache;
        }
        
        @Override
        public long getCurrentSize() {
            return cache.size();
        }
        
        @Override
        public double getUsagePercent() {
            return maxSize > 0 ? (double) getCurrentSize() / maxSize * 100 : 0;
        }
        
        @Override
        public String getStats() {
            return String.format(
                    "Map缓存[%s]: 大小=%d/%d (%.1f%%)",
                    name, getCurrentSize(), maxSize, getUsagePercent()
            );
        }
        
        @Override
        public boolean isHealthy() {
            double usage = getUsagePercent();
            return usage < config.getCacheHealthThreshold();
        }
        
        @Override
        public void cleanup() {
            if (cache instanceof ConcurrentHashMap) {
                // 对于ConcurrentHashMap，可以考虑部分清理
                logger.info("Map缓存 {} 不支持自动清理", name);
            }
        }
    }
    
    /**
     * 监控配置
     */
    public static class MonitorConfig {
        private boolean enablePeriodicLogging = true;
        private boolean enableHealthCheck = true;
        private boolean enableMemoryAlert = true;
        private boolean enableAutoCleanup = false;
        private long monitorInterval = 5; // 分钟
        private double memoryAlertThreshold = 80.0; // 百分比
        private double cacheHealthThreshold = 90.0; // 百分比
        private double cacheCleanupThreshold = 85.0; // 百分比
        
        // Getters and Setters
        public boolean isEnablePeriodicLogging() { return enablePeriodicLogging; }
        public void setEnablePeriodicLogging(boolean enablePeriodicLogging) { this.enablePeriodicLogging = enablePeriodicLogging; }
        
        public boolean isEnableHealthCheck() { return enableHealthCheck; }
        public void setEnableHealthCheck(boolean enableHealthCheck) { this.enableHealthCheck = enableHealthCheck; }
        
        public boolean isEnableMemoryAlert() { return enableMemoryAlert; }
        public void setEnableMemoryAlert(boolean enableMemoryAlert) { this.enableMemoryAlert = enableMemoryAlert; }
        
        public boolean isEnableAutoCleanup() { return enableAutoCleanup; }
        public void setEnableAutoCleanup(boolean enableAutoCleanup) { this.enableAutoCleanup = enableAutoCleanup; }
        
        public long getMonitorInterval() { return monitorInterval; }
        public void setMonitorInterval(long monitorInterval) { this.monitorInterval = monitorInterval; }
        
        public double getMemoryAlertThreshold() { return memoryAlertThreshold; }
        public void setMemoryAlertThreshold(double memoryAlertThreshold) { this.memoryAlertThreshold = memoryAlertThreshold; }
        
        public double getCacheHealthThreshold() { return cacheHealthThreshold; }
        public void setCacheHealthThreshold(double cacheHealthThreshold) { this.cacheHealthThreshold = cacheHealthThreshold; }
        
        public double getCacheCleanupThreshold() { return cacheCleanupThreshold; }
        public void setCacheCleanupThreshold(double cacheCleanupThreshold) { this.cacheCleanupThreshold = cacheCleanupThreshold; }
    }
    
    /**
     * 获取监控配置
     */
    public static MonitorConfig getConfig() {
        return config;
    }
    
    /**
     * 设置监控配置
     */
    public static void setConfig(MonitorConfig config) {
        CacheMonitor.config = config;
    }
}
