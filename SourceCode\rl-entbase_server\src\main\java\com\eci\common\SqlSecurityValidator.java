package com.eci.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Pattern;

/**
 * SQL安全验证器
 * 防止SQL注入攻击，验证SQL模板和参数的安全性
 */
public class SqlSecurityValidator {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlSecurityValidator.class);
    
    // 危险的SQL关键字
    private static final Set<String> DANGEROUS_KEYWORDS = new HashSet<>(Arrays.asList(
            "DROP", "DELETE", "TRUNCATE", "ALTER", "CREATE", "INSERT", "UPDATE",
            "EXEC", "EXECUTE", "UNION", "SCRIPT", "DECLARE", "CURSOR",
            "SHUTDOWN", "BACKUP", "RESTORE", "GRANT", "REVOKE"
    ));
    
    // 危险的SQL模式
    private static final List<Pattern> DANGEROUS_PATTERNS = Arrays.asList(
            Pattern.compile("(?i).*;\\s*(DROP|DELETE|TRUNCATE|ALTER|CREATE|INSERT|UPDATE)\\s+.*"),
            Pattern.compile("(?i).*\\bUNION\\s+SELECT\\b.*"),
            Pattern.compile("(?i).*\\bEXEC\\s*\\(.*\\).*"),
            Pattern.compile("(?i).*\\bSCRIPT\\s*\\(.*\\).*"),
            Pattern.compile("(?i).*--.*"),
            Pattern.compile("(?i).*/\\*.*\\*/.*"),
            Pattern.compile("(?i).*\\bxp_.*"),
            Pattern.compile("(?i).*\\bsp_.*")
    );
    
    // 允许的SQL操作类型
    private static final Set<String> ALLOWED_OPERATIONS = new HashSet<>(Arrays.asList(
            "SELECT", "WITH"
    ));
    
    /**
     * 验证SQL模板的安全性
     * 
     * @param sqlTemplate SQL模板
     * @return 验证结果
     */
    public static ValidationResult validateSqlTemplate(String sqlTemplate) {
        if (sqlTemplate == null || sqlTemplate.trim().isEmpty()) {
            return ValidationResult.error("SQL模板不能为空");
        }
        
        String normalizedSql = sqlTemplate.trim().toUpperCase();
        
        // 1. 检查是否以允许的操作开头
        boolean startsWithAllowed = ALLOWED_OPERATIONS.stream()
                .anyMatch(normalizedSql::startsWith);
        
        if (!startsWithAllowed) {
            return ValidationResult.error("SQL模板必须以SELECT或WITH开头");
        }
        
        // 2. 检查危险关键字
        for (String keyword : DANGEROUS_KEYWORDS) {
            if (normalizedSql.contains(keyword)) {
                return ValidationResult.error("SQL模板包含危险关键字: " + keyword);
            }
        }
        
        // 3. 检查危险模式
        for (Pattern pattern : DANGEROUS_PATTERNS) {
            if (pattern.matcher(sqlTemplate).matches()) {
                return ValidationResult.error("SQL模板匹配危险模式: " + pattern.pattern());
            }
        }
        
        // 4. 检查参数占位符格式
        if (!isValidParameterFormat(sqlTemplate)) {
            return ValidationResult.error("SQL模板参数格式不正确");
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 验证SQL参数的安全性
     * 
     * @param params SQL参数
     * @return 验证结果
     */
    public static ValidationResult validateSqlParameters(Map<String, Object> params) {
        if (params == null) {
            return ValidationResult.success();
        }
        
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            // 验证参数名
            if (!isValidParameterName(key)) {
                return ValidationResult.error("参数名不合法: " + key);
            }
            
            // 验证参数值
            ValidationResult valueResult = validateParameterValue(key, value);
            if (!valueResult.isValid()) {
                return valueResult;
            }
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 验证参数占位符格式
     */
    private static boolean isValidParameterFormat(String sql) {
        // 检查是否只包含 ${paramName} 格式的占位符
        Pattern validPattern = Pattern.compile("\\$\\{[a-zA-Z_][a-zA-Z0-9_]*\\}");
        Pattern invalidPattern = Pattern.compile("\\$\\{[^}]*[^a-zA-Z0-9_][^}]*\\}");
        
        return !invalidPattern.matcher(sql).find();
    }
    
    /**
     * 验证参数名是否合法
     */
    private static boolean isValidParameterName(String paramName) {
        if (paramName == null || paramName.trim().isEmpty()) {
            return false;
        }
        
        // 参数名只能包含字母、数字和下划线，且必须以字母或下划线开头
        return paramName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
    
    /**
     * 验证参数值
     */
    private static ValidationResult validateParameterValue(String paramName, Object value) {
        if (value == null) {
            return ValidationResult.success();
        }
        
        String stringValue = value.toString();
        
        // 检查长度限制
        if (stringValue.length() > 1000) {
            return ValidationResult.error("参数值过长: " + paramName);
        }
        
        // 检查是否包含危险字符
        if (containsDangerousCharacters(stringValue)) {
            return ValidationResult.error("参数值包含危险字符: " + paramName);
        }
        
        return ValidationResult.success();
    }
    
    /**
     * 检查是否包含危险字符
     */
    private static boolean containsDangerousCharacters(String value) {
        // 检查SQL注入常用的危险字符组合
        String[] dangerousPatterns = {
                "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_",
                "union", "select", "insert", "update", "delete", "drop"
        };
        
        String lowerValue = value.toLowerCase();
        for (String pattern : dangerousPatterns) {
            if (lowerValue.contains(pattern)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 清理和转义参数值
     */
    public static Object sanitizeParameterValue(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof String) {
            String stringValue = (String) value;
            // 移除或转义危险字符
            return stringValue
                    .replace("'", "''")  // SQL单引号转义
                    .replace("\"", "\\\"") // 双引号转义
                    .replace(";", "")      // 移除分号
                    .replace("--", "")     // 移除注释
                    .replace("/*", "")     // 移除注释开始
                    .replace("*/", "");    // 移除注释结束
        }
        
        return value;
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult error(String message) {
            return new ValidationResult(false, message);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        @Override
        public String toString() {
            return valid ? "验证通过" : "验证失败: " + errorMessage;
        }
    }
    
    /**
     * SQL安全配置
     */
    public static class SecurityConfig {
        private boolean enableValidation = true;
        private boolean enableParameterSanitization = true;
        private int maxParameterLength = 1000;
        private Set<String> allowedOperations = new HashSet<>(ALLOWED_OPERATIONS);
        private Set<String> blockedKeywords = new HashSet<>(DANGEROUS_KEYWORDS);
        
        public boolean isEnableValidation() {
            return enableValidation;
        }
        
        public void setEnableValidation(boolean enableValidation) {
            this.enableValidation = enableValidation;
        }
        
        public boolean isEnableParameterSanitization() {
            return enableParameterSanitization;
        }
        
        public void setEnableParameterSanitization(boolean enableParameterSanitization) {
            this.enableParameterSanitization = enableParameterSanitization;
        }
        
        public int getMaxParameterLength() {
            return maxParameterLength;
        }
        
        public void setMaxParameterLength(int maxParameterLength) {
            this.maxParameterLength = maxParameterLength;
        }
        
        public Set<String> getAllowedOperations() {
            return allowedOperations;
        }
        
        public void setAllowedOperations(Set<String> allowedOperations) {
            this.allowedOperations = allowedOperations;
        }
        
        public Set<String> getBlockedKeywords() {
            return blockedKeywords;
        }
        
        public void setBlockedKeywords(Set<String> blockedKeywords) {
            this.blockedKeywords = blockedKeywords;
        }
    }
}
